<!-- Action bar with create button -->
<vdr-page-block>
	<vdr-action-bar>
		<vdr-ab-left>
			<div class="flex items-center gap-2">
				<button
					class="btn btn-secondary btn-sm"
					(click)="refreshList()"
					[disabled]="loading$ | async"
				>
					<clr-icon shape="refresh" [class.spinning]="loading$ | async"></clr-icon>
					Refresh
				</button>
				<div *ngIf="loading$ | async" class="loading-indicator">
					<clr-icon shape="spinner" class="spinning"></clr-icon>
					Loading...
				</div>
			</div>
		</vdr-ab-left>
		<vdr-ab-right>
			<a class="btn btn-primary" vdrIfPermissions="CreateCatalog" [routerLink]="['./create']">
				<clr-icon shape="plus"></clr-icon>
				Create Decal
			</a>
		</vdr-ab-right>
	</vdr-action-bar>
</vdr-page-block>

<!-- Loading state -->
<div *ngIf="loading$ | async" class="loading-overlay">
	<div class="loading-content">
		<clr-icon shape="spinner" class="spinning"></clr-icon>
		<span>Loading decals...</span>
	</div>
</div>

<!-- Data table -->
<div *ngIf="decals$ | async as result" [class.loading-disabled]="loading$ | async">
	<vdr-data-table
		[items]="result.decals.items"
		[totalItems]="result.decals.totalItems">

		<vdr-dt-column id="asset" [heading]="'Image'" [hiddenByDefault]="false">
			<ng-template let-decal="item">
				<div class="asset-preview">
					<img
						*ngIf="decal.asset?.preview"
						[src]="decal.asset.preview + '?preset=tiny'"
						[alt]="decal.asset.name"
						class="asset-thumbnail"
					/>
					<span *ngIf="!decal.asset?.preview" class="no-image">No image</span>
				</div>
			</ng-template>
		</vdr-dt-column>

		<vdr-dt-column id="name" [heading]="'Name'">
			<ng-template let-decal="item">
				<a class="button-ghost" [routerLink]="['./', decal.id]">
					<span>{{ decal.name }}</span>
					<clr-icon shape="arrow right"></clr-icon>
				</a>
			</ng-template>
		</vdr-dt-column>

		<vdr-dt-column id="category" [heading]="'Category'">
			<ng-template let-decal="item">
				<span class="category-badge">{{ decal.category }}</span>
			</ng-template>
		</vdr-dt-column>

		<vdr-dt-column id="dimensions" [heading]="'Max Dimensions'">
			<ng-template let-decal="item">
				{{ decal.maxWidth }} × {{ decal.maxHeight }}
			</ng-template>
		</vdr-dt-column>

		<vdr-dt-column id="scale" [heading]="'Scale Range'">
			<ng-template let-decal="item">
				{{ decal.minScale }}× - {{ decal.maxScale }}×
			</ng-template>
		</vdr-dt-column>

		<vdr-dt-column id="status" [heading]="'Status'">
			<ng-template let-decal="item">
				<vdr-chip [colorType]="decal.isActive ? 'success' : 'warning'">
					{{ decal.isActive ? 'Active' : 'Inactive' }}
				</vdr-chip>
			</ng-template>
		</vdr-dt-column>

		<vdr-dt-column id="actions" [heading]="'Actions'" [hiddenByDefault]="false">
			<ng-template let-decal="item">
				<div class="action-buttons">
					<a
						class="btn btn-sm btn-secondary"
						[routerLink]="['./', decal.id]"
						title="Edit decal"
					>
						<clr-icon shape="edit"></clr-icon>
					</a>
					<button
						class="btn btn-sm btn-danger"
						vdrIfPermissions="DeleteCatalog"
						(click)="deleteDecal(decal)"
						[disabled]="loading$ | async"
						title="Delete decal"
					>
						<clr-icon shape="trash"></clr-icon>
					</button>
				</div>
			</ng-template>
		</vdr-dt-column>
	</vdr-data-table>
</div>

<!-- Empty state -->
<div *ngIf="(decals$ | async)?.decals?.items?.length === 0 && !(loading$ | async)" class="empty-state">
	<div class="empty-state-content">
		<clr-icon shape="image" size="48"></clr-icon>
		<h3>No decals found</h3>
		<p>Get started by creating your first decal.</p>
		<a class="btn btn-primary" vdrIfPermissions="CreateCatalog" [routerLink]="['./create']">
			<clr-icon shape="plus"></clr-icon>
			Create Decal
		</a>
	</div>
</div>
