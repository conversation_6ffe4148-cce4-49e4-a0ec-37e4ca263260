import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { DataService, SharedModule } from '@vendure/admin-ui/core';
import { gql } from 'apollo-angular';
import { Observable } from 'rxjs';

const GET_DECAL_LIST = gql`
	query GetDecalList {
		decals {
			items {
				id
				createdAt
				updatedAt
				name
				description
				category
				isActive
				maxWidth
				maxHeight
				minScale
				maxScale
				asset {
					id
					name
					preview
				}
			}
			totalItems
		}
	}
`;

@Component({
	selector: 'decal-list',
	templateUrl: './decal-list.component.html',
	styleUrls: ['./decal-list.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	standalone: true,
	imports: [SharedModule],
})
export class DecalListComponent implements OnInit {
	decals$: Observable<any>;

	constructor(private dataService: DataService) {}

	ngOnInit() {
		this.decals$ = this.dataService.query(GET_DECAL_LIST).stream$;
	}
}
