import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { DataService, NotificationService, SharedModule } from '@vendure/admin-ui/core';
import { gql } from 'apollo-angular';
import { Observable } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';

export const getDecalDetailDocument = gql`
	query GetDecalDetail($id: ID!) {
		decal(id: $id) {
			id
			createdAt
			updatedAt
			name
			description
			category
			isActive
			maxWidth
			maxHeight
			minScale
			maxScale
			asset {
				id
				name
				preview
				source
			}
		}
	}
`;

const CREATE_DECAL = gql`
	mutation CreateDecal($input: CreateDecalInput!) {
		createDecal(input: $input) {
			id
			name
			description
			category
			isActive
			maxWidth
			maxHeight
			minScale
			maxScale
			asset {
				id
				name
				preview
			}
		}
	}
`;

const UPDATE_DECAL = gql`
	mutation UpdateDecal($input: UpdateDecalInput!) {
		updateDecal(input: $input) {
			id
			name
			description
			category
			isActive
			maxWidth
			maxHeight
			minScale
			maxScale
			asset {
				id
				name
				preview
			}
		}
	}
`;

@Component({
	selector: 'decal-detail',
	templateUrl: './decal-detail.component.html',
	styleUrls: ['./decal-detail.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	standalone: true,
	imports: [SharedModule],
})
export class DecalDetailComponent implements OnInit {
	detailForm = this.formBuilder.group({
		name: ['', Validators.required],
		description: [''],
		category: ['', Validators.required],
		assetId: ['', Validators.required],
		isActive: [true],
		maxWidth: [100, [Validators.required, Validators.min(1)]],
		maxHeight: [100, [Validators.required, Validators.min(1)]],
		minScale: [0.5, [Validators.required, Validators.min(0.1)]],
		maxScale: [2.0, [Validators.required, Validators.min(0.1)]],
	});

	entity$: Observable<any>;
	isNew$: Observable<boolean>;

	constructor(
		private formBuilder: FormBuilder,
		private notificationService: NotificationService,
		private dataService: DataService,
		private route: ActivatedRoute,
		private router: Router
	) {}

	ngOnInit() {
		this.isNew$ = this.route.paramMap.pipe(map((params) => params.get('id') === 'create'));

		this.entity$ = this.route.paramMap.pipe(
			switchMap((params) => {
				const id = params.get('id');
				if (id && id !== 'create') {
					return this.dataService.query(getDecalDetailDocument, { id }).stream$;
				}
				return new Observable((observer) => observer.next(null));
			})
		);

		// Subscribe to entity changes to populate form
		this.entity$.subscribe((result) => {
			if (result?.decal) {
				this.setFormValues(result.decal);
			}
		});
	}

	create() {
		const formValue = this.detailForm.value;
		if (!formValue.name || !formValue.category || !formValue.assetId) {
			return;
		}

		this.dataService
			.mutate(CREATE_DECAL, {
				input: {
					name: formValue.name,
					description: formValue.description || '',
					category: formValue.category,
					assetId: formValue.assetId,
					maxWidth: formValue.maxWidth || 100,
					maxHeight: formValue.maxHeight || 100,
					minScale: formValue.minScale || 0.5,
					maxScale: formValue.maxScale || 2.0,
				},
			})
			.stream$.subscribe((result: any) => {
				if (result.createDecal?.id) {
					this.notificationService.success('Decal created successfully');
					this.router.navigate(['extensions', 'decal', result.createDecal.id]);
				}
			});
	}

	update() {
		const formValue = this.detailForm.value;

		// Get current ID from route
		const currentId = this.route.snapshot.paramMap.get('id');
		if (!currentId || currentId === 'create') {
			return;
		}

		this.dataService
			.mutate(UPDATE_DECAL, {
				input: {
					id: currentId,
					name: formValue.name,
					description: formValue.description,
					category: formValue.category,
					assetId: formValue.assetId,
					isActive: formValue.isActive,
					maxWidth: formValue.maxWidth,
					maxHeight: formValue.maxHeight,
					minScale: formValue.minScale,
					maxScale: formValue.maxScale,
				},
			})
			.stream$.subscribe(() => {
				this.notificationService.success('Decal updated successfully');
			});
	}

	private setFormValues(entity: any): void {
		this.detailForm.patchValue({
			name: entity.name,
			description: entity.description,
			category: entity.category,
			assetId: entity.asset?.id,
			isActive: entity.isActive,
			maxWidth: entity.maxWidth,
			maxHeight: entity.maxHeight,
			minScale: entity.minScale,
			maxScale: entity.maxScale,
		});
	}
}
