import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {
	DataService,
	ModalService,
	NotificationService,
	SharedModule,
} from '@vendure/admin-ui/core';
import { gql } from 'apollo-angular';
import { BehaviorSubject, Observable } from 'rxjs';
import { finalize, map, switchMap } from 'rxjs/operators';

export const getDecalDetailDocument = gql`
	query GetDecalDetail($id: ID!) {
		decal(id: $id) {
			id
			createdAt
			updatedAt
			name
			description
			category
			isActive
			maxWidth
			maxHeight
			minScale
			maxScale
			asset {
				id
				name
				preview
				source
			}
		}
	}
`;

const CREATE_DECAL = gql`
	mutation CreateDecal($input: CreateDecalInput!) {
		createDecal(input: $input) {
			id
			name
			description
			category
			isActive
			maxWidth
			maxHeight
			minScale
			maxScale
			asset {
				id
				name
				preview
			}
		}
	}
`;

const UPDATE_DECAL = gql`
	mutation UpdateDecal($input: UpdateDecalInput!) {
		updateDecal(input: $input) {
			id
			name
			description
			category
			isActive
			maxWidth
			maxHeight
			minScale
			maxScale
			asset {
				id
				name
				preview
			}
		}
	}
`;

const DELETE_DECAL = gql`
	mutation DeleteDecal($id: ID!) {
		deleteDecal(id: $id) {
			result
			message
		}
	}
`;

@Component({
	selector: 'decal-detail',
	templateUrl: './decal-detail.component.html',
	styleUrls: ['./decal-detail.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	standalone: true,
	imports: [SharedModule],
})
export class DecalDetailComponent implements OnInit {
	detailForm: any;
	entity$: Observable<any>;
	isNew$: Observable<boolean>;
	loading$ = new BehaviorSubject<boolean>(false);
	saving$ = new BehaviorSubject<boolean>(false);

	constructor(
		private formBuilder: FormBuilder,
		private notificationService: NotificationService,
		private dataService: DataService,
		private route: ActivatedRoute,
		private router: Router,
		private modalService: ModalService
	) {
		this.detailForm = this.formBuilder.group({
			name: ['', Validators.required],
			description: [''],
			category: ['', Validators.required],
			assetId: ['', Validators.required],
			isActive: [true],
			maxWidth: [100, [Validators.required, Validators.min(1)]],
			maxHeight: [100, [Validators.required, Validators.min(1)]],
			minScale: [0.5, [Validators.required, Validators.min(0.1)]],
			maxScale: [2.0, [Validators.required, Validators.min(0.1)]],
		});
	}

	ngOnInit() {
		this.isNew$ = this.route.paramMap.pipe(map((params) => params.get('id') === 'create'));

		this.entity$ = this.route.paramMap.pipe(
			switchMap((params) => {
				const id = params.get('id');
				if (id && id !== 'create') {
					return this.dataService.query(getDecalDetailDocument, { id }).stream$;
				}
				return new Observable((observer) => observer.next(null));
			})
		);

		// Subscribe to entity changes to populate form
		this.entity$.subscribe((result) => {
			if (result?.decal) {
				this.setFormValues(result.decal);
			}
		});
	}

	create() {
		const formValue = this.detailForm.value;
		if (!formValue.name || !formValue.category || !formValue.assetId) {
			return;
		}

		this.saving$.next(true);
		this.dataService
			.mutate(CREATE_DECAL, {
				input: {
					name: formValue.name,
					description: formValue.description || '',
					category: formValue.category,
					assetId: formValue.assetId,
					maxWidth: formValue.maxWidth || 100,
					maxHeight: formValue.maxHeight || 100,
					minScale: formValue.minScale || 0.5,
					maxScale: formValue.maxScale || 2.0,
				},
			})
			.pipe(finalize(() => this.saving$.next(false)))
			.subscribe({
				next: (result: any) => {
					if (result.createDecal?.id) {
						this.notificationService.success('Decal created successfully');
						this.router.navigate(['extensions', 'decal', result.createDecal.id]);
					}
				},
				error: (error: any) => {
					this.notificationService.error(
						'Error creating decal: ' + (error.message || 'Unknown error')
					);
				},
			});
	}

	update() {
		const formValue = this.detailForm.value;

		// Get current ID from route
		const currentId = this.route.snapshot.paramMap.get('id');
		if (!currentId || currentId === 'create') {
			return;
		}

		this.saving$.next(true);
		this.dataService
			.mutate(UPDATE_DECAL, {
				input: {
					id: currentId,
					name: formValue.name,
					description: formValue.description,
					category: formValue.category,
					assetId: formValue.assetId,
					isActive: formValue.isActive,
					maxWidth: formValue.maxWidth,
					maxHeight: formValue.maxHeight,
					minScale: formValue.minScale,
					maxScale: formValue.maxScale,
				},
			})
			.pipe(finalize(() => this.saving$.next(false)))
			.subscribe({
				next: () => {
					this.notificationService.success('Decal updated successfully');
					this.detailForm.markAsPristine();
				},
				error: (error: any) => {
					this.notificationService.error(
						'Error updating decal: ' + (error.message || 'Unknown error')
					);
				},
			});
	}

	async delete() {
		const currentId = this.route.snapshot.paramMap.get('id');
		if (!currentId || currentId === 'create') {
			return;
		}

		const entity = await this.entity$.pipe(map((result) => result?.decal)).toPromise();
		if (!entity) {
			return;
		}

		const result = await this.modalService
			.dialog({
				title: 'Delete Decal',
				body: `Are you sure you want to delete "${entity.name}"? This action cannot be undone.`,
				buttons: [
					{ type: 'secondary', label: 'Cancel' },
					{ type: 'danger', label: 'Delete', returnValue: true },
				],
			})
			.pipe(map((res) => res))
			.toPromise();

		if (result) {
			this.saving$.next(true);
			this.dataService
				.mutate(DELETE_DECAL, { id: currentId })
				.pipe(finalize(() => this.saving$.next(false)))
				.subscribe({
					next: (deleteResult: any) => {
						if (deleteResult.deleteDecal?.result === 'DELETED') {
							this.notificationService.success('Decal deleted successfully');
							this.router.navigate(['extensions', 'decal']);
						} else {
							this.notificationService.error('Failed to delete decal');
						}
					},
					error: (error: any) => {
						this.notificationService.error(
							'Error deleting decal: ' + (error.message || 'Unknown error')
						);
					},
				});
		}
	}

	onAssetChange(assets: any[]) {
		const assetId = assets.length > 0 ? assets[0].id : '';
		this.detailForm.get('assetId')?.setValue(assetId);
		this.detailForm.get('assetId')?.markAsDirty();
	}

	private setFormValues(entity: any): void {
		this.detailForm.patchValue({
			name: entity.name,
			description: entity.description,
			category: entity.category,
			assetId: entity.asset?.id,
			isActive: entity.isActive,
			maxWidth: entity.maxWidth,
			maxHeight: entity.maxHeight,
			minScale: entity.minScale,
			maxScale: entity.maxScale,
		});
	}
}
