.asset-preview-large {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 16px;
	background-color: var(--color-grey-50);
	border-radius: 4px;
	min-height: 200px;
}

.asset-preview-container {
	position: relative;
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 120px;
}

.asset-preview-img {
	max-width: 100%;
	max-height: 200px;
	border-radius: 4px;
	border: 1px solid var(--color-grey-300);
	margin-bottom: 12px;
}

.image-error {
	text-align: center;
	color: var(--color-error-500);
	padding: 20px;
}

.image-error clr-icon {
	color: var(--color-error-500);
	margin-bottom: 8px;
}

.image-error p {
	margin: 0 0 4px 0;
	font-size: 14px;
	font-weight: 500;
}

.image-error small {
	font-size: 12px;
	color: var(--color-error-400);
}

.preview-info {
	text-align: center;
}

.preview-label {
	margin: 0 0 4px 0;
	font-size: 14px;
	font-weight: 500;
	color: var(--color-grey-700);
}

.preview-id {
	font-size: 12px;
	color: var(--color-grey-500);
	font-family: monospace;
}

.no-image-placeholder {
	text-align: center;
	color: var(--color-grey-500);
}

.no-image-placeholder clr-icon {
	color: var(--color-grey-400);
	margin-bottom: 12px;
}

.no-image-placeholder p {
	margin: 0 0 4px 0;
	font-size: 14px;
	font-weight: 500;
}

.no-image-placeholder small {
	font-size: 12px;
}

.asset-upload-section {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.upload-help-text {
	padding: 12px;
	background-color: var(--color-grey-50);
	border-radius: 4px;
	border-left: 3px solid var(--color-primary-500);
}

.upload-help-text p {
	margin: 0 0 4px 0;
	font-size: 13px;
	color: var(--color-grey-600);
}

.upload-help-text p:last-child {
	margin-bottom: 0;
}

.asset-picker-placeholder {
	padding: 16px;
	border: 2px dashed var(--color-grey-300);
	border-radius: 4px;
	text-align: center;
	background-color: var(--color-grey-50);
	margin-bottom: 12px;
}

.asset-picker-placeholder p {
	margin: 0 0 8px 0;
	color: var(--color-grey-600);
	font-size: 14px;
}

.asset-id-input {
	width: 100%;
	max-width: 300px;
	padding: 8px 12px;
	border: 1px solid var(--color-grey-300);
	border-radius: 4px;
	font-size: 14px;
	margin-top: 8px;
}

.asset-id-input:focus {
	outline: none;
	border-color: var(--color-primary-500);
	box-shadow: 0 0 0 2px var(--color-primary-100);
}

.asset-manager-btn {
	margin-top: 12px;
	display: inline-flex;
	align-items: center;
	gap: 6px;
}

.upload-help-text ol {
	text-align: left;
	margin: 8px 0;
	padding-left: 20px;
}

.upload-help-text li {
	margin-bottom: 4px;
	font-size: 13px;
	color: var(--color-grey-600);
}

.upload-help-text code {
	background-color: var(--color-grey-100);
	padding: 2px 4px;
	border-radius: 3px;
	font-family: monospace;
	font-size: 12px;
}

.action-buttons {
	display: flex;
	gap: 8px;
	align-items: center;
}

.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(255, 255, 255, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12px;
	padding: 24px;
	background: white;
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	font-size: 14px;
	color: var(--color-grey-700);
}

.loading-disabled {
	opacity: 0.6;
	pointer-events: none;
}

.spinning {
	animation: spin 1s linear infinite;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.form-grid {
	display: grid;
	grid-template-columns: repeat(12, 1fr);
	gap: 16px;
	align-items: start;
}

.form-grid vdr-form-field {
	grid-column: span 6;
}

.form-grid vdr-form-field.full-width {
	grid-column: span 12;
}

.is-invalid {
	border-color: var(--color-error-500);
}

.is-invalid:focus {
	border-color: var(--color-error-500);
	box-shadow: 0 0 0 2px var(--color-error-100);
}
