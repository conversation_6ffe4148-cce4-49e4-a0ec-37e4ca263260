.asset-preview-large {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 16px;
	background-color: var(--color-grey-50);
	border-radius: 4px;
	min-height: 200px;
}

.asset-preview-img {
	max-width: 100%;
	max-height: 200px;
	border-radius: 4px;
	border: 1px solid var(--color-grey-300);
}

.form-grid {
	display: grid;
	grid-template-columns: repeat(12, 1fr);
	gap: 16px;
	align-items: start;
}

.form-grid vdr-form-field {
	grid-column: span 6;
}

.form-grid vdr-form-field[span="12"] {
	grid-column: span 12;
}

.is-invalid {
	border-color: var(--color-error-500);
}

.is-invalid:focus {
	border-color: var(--color-error-500);
	box-shadow: 0 0 0 2px var(--color-error-100);
}
