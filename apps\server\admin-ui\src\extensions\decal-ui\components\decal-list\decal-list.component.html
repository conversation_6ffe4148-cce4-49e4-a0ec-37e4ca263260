<!-- Action bar with create button -->
<vdr-page-block>
	<vdr-action-bar>
		<vdr-ab-left></vdr-ab-left>
		<vdr-ab-right>
			<a class="btn btn-primary" *vdrIfPermissions="['CreateCatalog']" [routerLink]="['./create']">
				<clr-icon shape="plus"></clr-icon>
				Create Decal
			</a>
		</vdr-ab-right>
	</vdr-action-bar>
</vdr-page-block>

<!-- Data table -->
<div *ngIf="decals$ | async as result">
	<vdr-data-table
		[items]="result.decals.items"
		[totalItems]="result.decals.totalItems">

		<vdr-dt-column id="asset" [heading]="'Image'" [hiddenByDefault]="false">
			<ng-template let-decal="item">
				<div class="asset-preview">
					<img
						*ngIf="decal.asset?.preview"
						[src]="decal.asset.preview + '?preset=tiny'"
						[alt]="decal.asset.name"
						class="asset-thumbnail"
					/>
					<span *ngIf="!decal.asset?.preview" class="no-image">No image</span>
				</div>
			</ng-template>
		</vdr-dt-column>

		<vdr-dt-column id="name" [heading]="'Name'">
			<ng-template let-decal="item">
				<a class="button-ghost" [routerLink]="['./', decal.id]">
					<span>{{ decal.name }}</span>
					<clr-icon shape="arrow right"></clr-icon>
				</a>
			</ng-template>
		</vdr-dt-column>

		<vdr-dt-column id="category" [heading]="'Category'">
			<ng-template let-decal="item">
				<span class="category-badge">{{ decal.category }}</span>
			</ng-template>
		</vdr-dt-column>

		<vdr-dt-column id="dimensions" [heading]="'Max Dimensions'">
			<ng-template let-decal="item">
				{{ decal.maxWidth }} × {{ decal.maxHeight }}
			</ng-template>
		</vdr-dt-column>

		<vdr-dt-column id="scale" [heading]="'Scale Range'">
			<ng-template let-decal="item">
				{{ decal.minScale }}× - {{ decal.maxScale }}×
			</ng-template>
		</vdr-dt-column>

		<vdr-dt-column id="status" [heading]="'Status'">
			<ng-template let-decal="item">
				<vdr-chip [colorType]="decal.isActive ? 'success' : 'warning'">
					{{ decal.isActive ? 'Active' : 'Inactive' }}
				</vdr-chip>
			</ng-template>
		</vdr-dt-column>
	</vdr-data-table>
</div>
