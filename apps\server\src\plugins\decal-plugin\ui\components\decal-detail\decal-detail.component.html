<!-- Action bar -->
<vdr-page-block>
	<vdr-action-bar>
		<vdr-ab-left>
			<div *ngIf="entity$ | async as result">
				<vdr-entity-info *ngIf="result?.decal" [entity]="result.decal"></vdr-entity-info>
			</div>
		</vdr-ab-left>
		<vdr-ab-right>
			<div class="action-buttons">
				<button
					class="btn btn-danger"
					*ngIf="!(isNew$ | async) && (entity$ | async)?.decal"
					vdrIfPermissions="DeleteCatalog"
					(click)="delete()"
					[disabled]="saving$ | async"
				>
					<clr-icon shape="trash"></clr-icon>
					Delete
				</button>
				<button
					class="btn btn-primary"
					*ngIf="isNew$ | async; else updateButton"
					(click)="create()"
					[disabled]="detailForm.pristine || detailForm.invalid || (saving$ | async)"
				>
					<clr-icon *ngIf="saving$ | async" shape="spinner" class="spinning"></clr-icon>
					<clr-icon *ngIf="!(saving$ | async)" shape="plus"></clr-icon>
					{{ (saving$ | async) ? 'Creating...' : 'Create' }}
				</button>
				<ng-template #updateButton>
					<button
						class="btn btn-primary"
						(click)="update()"
						[disabled]="detailForm.pristine || detailForm.invalid || (saving$ | async)"
					>
						<clr-icon *ngIf="saving$ | async" shape="spinner" class="spinning"></clr-icon>
						<clr-icon *ngIf="!(saving$ | async)" shape="check"></clr-icon>
						{{ (saving$ | async) ? 'Saving...' : 'Save' }}
					</button>
				</ng-template>
			</div>
		</vdr-ab-right>
	</vdr-action-bar>
</vdr-page-block>

<!-- Loading overlay -->
<div *ngIf="saving$ | async" class="loading-overlay">
	<div class="loading-content">
		<clr-icon shape="spinner" class="spinning"></clr-icon>
		<span>{{ (isNew$ | async) ? 'Creating decal...' : 'Saving changes...' }}</span>
	</div>
</div>

<!-- Form content -->
<form class="form" [formGroup]="detailForm" [class.loading-disabled]="saving$ | async">
	<vdr-page-detail-layout>
		<!-- Sidebar -->
		<vdr-page-detail-sidebar>
			<div *ngIf="entity$ | async as result">
				<vdr-card *ngIf="result?.decal">
					<vdr-page-entity-info [entity]="result.decal" />
				</vdr-card>
			</div>

			<!-- Asset preview -->
			<vdr-card title="Image Preview">
				<div class="asset-preview-large">
					<div *ngIf="detailForm.get('assetId')?.value; else noImagePlaceholder">
						<img
							[src]="'/admin-api/assets/' + detailForm.get('assetId')?.value + '?preset=medium'"
							alt="Decal preview"
							class="asset-preview-img"
							(error)="$event.target.style.display='none'"
						/>
						<div class="preview-info">
							<p class="preview-label">Current Image</p>
							<small class="preview-id">ID: {{ detailForm.get('assetId')?.value }}</small>
						</div>
					</div>
					<ng-template #noImagePlaceholder>
						<div class="no-image-placeholder">
							<clr-icon shape="image" size="48"></clr-icon>
							<p>No image selected</p>
							<small>Upload an image using the asset picker below</small>
						</div>
					</ng-template>
				</div>
			</vdr-card>
		</vdr-page-detail-sidebar>

		<!-- Main content -->
		<vdr-page-block>
			<!-- Basic Information -->
			<vdr-card title="Basic Information">
				<div class="form-grid">
					<vdr-form-field label="Name" for="name">
						<input
							id="name"
							type="text"
							formControlName="name"
							[class.is-invalid]="detailForm.get('name')?.invalid && detailForm.get('name')?.touched"
						/>
						<vdr-form-field-error
							*ngIf="detailForm.get('name')?.hasError('required')"
						>
							Name is required
						</vdr-form-field-error>
					</vdr-form-field>

					<vdr-form-field label="Category" for="category">
						<input
							id="category"
							type="text"
							formControlName="category"
							[class.is-invalid]="detailForm.get('category')?.invalid && detailForm.get('category')?.touched"
						/>
						<vdr-form-field-error
							*ngIf="detailForm.get('category')?.hasError('required')"
						>
							Category is required
						</vdr-form-field-error>
					</vdr-form-field>

					<vdr-form-field label="Description" for="description" class="full-width">
						<textarea
							id="description"
							formControlName="description"
							rows="3"
						></textarea>
					</vdr-form-field>

					<vdr-form-field label="Active" for="isActive">
						<clr-toggle-wrapper>
							<input
								type="checkbox"
								clrToggle
								id="isActive"
								formControlName="isActive"
							/>
							<label for="isActive">Active</label>
						</clr-toggle-wrapper>
					</vdr-form-field>
				</div>
			</vdr-card>

			<!-- Asset Selection -->
			<vdr-card title="Decal Image">
				<div class="form-grid">
					<vdr-form-field label="Image" for="assetId" class="full-width">
						<div class="asset-upload-section">
							<input
								type="hidden"
								formControlName="assetId"
								id="assetId"
							/>
							<div class="asset-picker-placeholder">
								<p>Use the Vendure admin asset manager to upload and select images.</p>
								<p>Asset ID: {{ detailForm.get('assetId')?.value || 'None selected' }}</p>
								<input
									type="text"
									placeholder="Enter Asset ID"
									[value]="detailForm.get('assetId')?.value"
									(input)="detailForm.get('assetId')?.setValue($event.target.value)"
									class="asset-id-input"
								/>
							</div>
							<div class="upload-help-text">
								<p>Upload images through the Vendure admin asset manager, then enter the Asset ID here.</p>
								<p>Recommended: High-resolution images with transparent backgrounds work best.</p>
							</div>
						</div>
						<vdr-form-field-error
							*ngIf="detailForm.get('assetId')?.hasError('required')"
						>
							Image is required
						</vdr-form-field-error>
					</vdr-form-field>
				</div>
			</vdr-card>

			<!-- Dimensions & Scaling -->
			<vdr-card title="Dimensions & Scaling">
				<div class="form-grid">
					<vdr-form-field label="Max Width" for="maxWidth">
						<input
							id="maxWidth"
							type="number"
							formControlName="maxWidth"
							min="1"
							step="0.1"
							[class.is-invalid]="detailForm.get('maxWidth')?.invalid && detailForm.get('maxWidth')?.touched"
						/>
						<vdr-form-field-error
							*ngIf="detailForm.get('maxWidth')?.hasError('required')"
						>
							Max width is required
						</vdr-form-field-error>
						<vdr-form-field-error
							*ngIf="detailForm.get('maxWidth')?.hasError('min')"
						>
							Max width must be at least 1
						</vdr-form-field-error>
					</vdr-form-field>

					<vdr-form-field label="Max Height" for="maxHeight">
						<input
							id="maxHeight"
							type="number"
							formControlName="maxHeight"
							min="1"
							step="0.1"
							[class.is-invalid]="detailForm.get('maxHeight')?.invalid && detailForm.get('maxHeight')?.touched"
						/>
						<vdr-form-field-error
							*ngIf="detailForm.get('maxHeight')?.hasError('required')"
						>
							Max height is required
						</vdr-form-field-error>
						<vdr-form-field-error
							*ngIf="detailForm.get('maxHeight')?.hasError('min')"
						>
							Max height must be at least 1
						</vdr-form-field-error>
					</vdr-form-field>

					<vdr-form-field label="Min Scale" for="minScale">
						<input
							id="minScale"
							type="number"
							formControlName="minScale"
							min="0.1"
							step="0.1"
							[class.is-invalid]="detailForm.get('minScale')?.invalid && detailForm.get('minScale')?.touched"
						/>
						<vdr-form-field-error
							*ngIf="detailForm.get('minScale')?.hasError('required')"
						>
							Min scale is required
						</vdr-form-field-error>
						<vdr-form-field-error
							*ngIf="detailForm.get('minScale')?.hasError('min')"
						>
							Min scale must be at least 0.1
						</vdr-form-field-error>
					</vdr-form-field>

					<vdr-form-field label="Max Scale" for="maxScale">
						<input
							id="maxScale"
							type="number"
							formControlName="maxScale"
							min="0.1"
							step="0.1"
							[class.is-invalid]="detailForm.get('maxScale')?.invalid && detailForm.get('maxScale')?.touched"
						/>
						<vdr-form-field-error
							*ngIf="detailForm.get('maxScale')?.hasError('required')"
						>
							Max scale is required
						</vdr-form-field-error>
						<vdr-form-field-error
							*ngIf="detailForm.get('maxScale')?.hasError('min')"
						>
							Max scale must be at least 0.1
						</vdr-form-field-error>
					</vdr-form-field>
				</div>
			</vdr-card>
		</vdr-page-block>
	</vdr-page-detail-layout>
</form>
